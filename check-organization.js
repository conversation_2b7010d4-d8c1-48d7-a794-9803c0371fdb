// Simple script to check organization data in database
const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// Organization schema (simplified)
const organizationSchema = new mongoose.Schema({
  org_id: String,
  user_id: String,
  org_name: String,
  org_type: String,
  provider: String,
  installation_id: String,
  installation_status: String,
  repos: Array,
  created_at: Date,
  updated_at: Date
});

const Organization = mongoose.model('Organization', organizationSchema);

async function checkOrganization() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find all organizations
    const allOrgs = await Organization.find({});
    console.log(`Found ${allOrgs.length} organizations in database`);

    if (allOrgs.length > 0) {
      console.log('\nAll organizations:');
      allOrgs.forEach((org, index) => {
        console.log(`${index + 1}. Organization ID: ${org.org_id}, Installation ID: ${org.installation_id}, Name: ${org.org_name}`);
      });
    }

    // Find the organization with installation ID 79202850
    const org = await Organization.findOne({ installation_id: '79202850' });

    if (org) {
      console.log('\n✅ Found organization with installation ID 79202850:');
      console.log('- Organization ID:', org.org_id);
      console.log('- Organization Name:', org.org_name);
      console.log('- Installation ID:', org.installation_id);
      console.log('- Installation Status:', org.installation_status);
      console.log('- Provider:', org.provider);
      console.log('- Repositories count:', org.repos?.length || 0);
      console.log('- Created at:', org.created_at);
      console.log('- Updated at:', org.updated_at);

      if (org.org_id === '76823457') {
        console.log('✅ Organization ID matches expected value from webhook');
      } else {
        console.log('❌ Organization ID does not match expected value');
        console.log('Expected: 76823457, Found:', org.org_id);
      }
    } else {
      console.log('\n❌ No organization found with installation ID 79202850');

      // Check if there's an organization with org_id 76823457
      const orgByOrgId = await Organization.findOne({ org_id: '76823457' });
      if (orgByOrgId) {
        console.log('✅ Found organization with org_id 76823457:');
        console.log('- Installation ID:', orgByOrgId.installation_id);
        console.log('- Organization Name:', orgByOrgId.org_name);
        console.log('- Installation Status:', orgByOrgId.installation_status);
      }
    }

    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkOrganization();
